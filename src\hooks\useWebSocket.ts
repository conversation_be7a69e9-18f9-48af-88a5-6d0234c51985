import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { io, Socket } from 'socket.io-client';
import {
  getWebSocketUrl,
  formatConnectionError,
  isRecoverableError,
  calculateReconnectDelay,
  createWebSocketOptions,
  logWebSocketEvent,
  WEBSOCKET_EVENTS
} from '../utils/websocketUtils';

interface FileUpdate {
  status: string;
  progress?: number;
  metadata?: Record<string, any>;
  timestamp: string;
}

interface ProcessingProgress {
  stage: string;
  progress: number;
  message: string;
  timestamp: string;
}

interface ChatStream {
  text: string;
  isComplete: boolean;
  metadata?: Record<string, any>;
  timestamp?: string;
}

export const useWebSocket = (userId: string | null) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [fileUpdates, setFileUpdates] = useState<Record<string, FileUpdate>>({});
  const [processingProgress, setProcessingProgress] = useState<Record<string, ProcessingProgress>>({});
  const [chatStreams, setChatStreams] = useState<Record<string, ChatStream>>({});
  const [systemMessages, setSystemMessages] = useState<Array<{
    id: string;
    type: string;
    message: string;
    timestamp: string;
    metadata?: any;
  }>>([]);

  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxReconnectAttempts = 5;
  const reconnectAttempts = useRef(0);

  // Track joined rooms to prevent duplicates
  const joinedFileRooms = useRef(new Set<string>());
  const joinedChatRooms = useRef(new Set<string>());

  // Initialize WebSocket connection
  const connect = useCallback(() => {
    if (!userId || socketRef.current?.connected) return;

    try {
      const wsUrl = getWebSocketUrl();
      logWebSocketEvent('Connecting', { url: wsUrl, userId });

      const socket = io(wsUrl, createWebSocketOptions(userId));

      socketRef.current = socket;

      // Connection handlers
      socket.on(WEBSOCKET_EVENTS.CONNECT, () => {
        logWebSocketEvent('Connected');
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttempts.current = 0;
        // Clear joined rooms on new connection
        joinedFileRooms.current.clear();
        joinedChatRooms.current.clear();
      });

      socket.on(WEBSOCKET_EVENTS.DISCONNECT, (reason) => {
        logWebSocketEvent('Disconnected', { reason });
        setIsConnected(false);
        // Clear joined rooms on disconnect
        joinedFileRooms.current.clear();
        joinedChatRooms.current.clear();

        // Auto-reconnect logic for recoverable disconnections
        if (reason !== 'io server disconnect' &&
            reconnectAttempts.current < maxReconnectAttempts &&
            isRecoverableError(reason)) {
          reconnectAttempts.current++;
          const delay = calculateReconnectDelay(reconnectAttempts.current);

          logWebSocketEvent('Scheduling reconnection', {
            attempt: reconnectAttempts.current,
            delay
          });

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        }
      });

      socket.on(WEBSOCKET_EVENTS.CONNECT_ERROR, (error) => {
        const formattedError = formatConnectionError(error);
        logWebSocketEvent('Connection error', { error: formattedError });
        setConnectionError(formattedError);
        setIsConnected(false);
      });

      // File processing events
      socket.on(WEBSOCKET_EVENTS.FILE_STATUS_UPDATE, (data: any) => {
        logWebSocketEvent('File status update', { fileId: data.file_id, status: data.status });
        setFileUpdates(prev => ({
          ...prev,
          [data.file_id]: {
            ...prev[data.file_id],
            status: data.status,
            progress: data.progress,
            metadata: data.metadata,
            timestamp: new Date().toISOString()
          }
        }));
      });

      socket.on(WEBSOCKET_EVENTS.PROCESSING_PROGRESS, (data: any) => {
        logWebSocketEvent('Processing progress', {
          fileId: data.file_id,
          stage: data.stage,
          progress: data.progress
        });
        setProcessingProgress(prev => ({
          ...prev,
          [data.file_id]: {
            stage: data.stage,
            progress: data.progress,
            message: data.message,
            timestamp: new Date().toISOString()
          }
        }));
      });

      // Chat streaming events
      socket.on(WEBSOCKET_EVENTS.CHAT_RESPONSE_CHUNK, (data: any) => {
        try {
          logWebSocketEvent('Chat response chunk received', {
            sessionId: data.chat_session_id,
            chunkLength: data.chunk?.length || 0,
            isFinal: data.is_final,
            hasMetadata: !!data.metadata
          });

          setChatStreams(prev => {
            const sessionId = data.chat_session_id;
            if (!sessionId) {
              console.warn('Received chat chunk without session ID:', data);
              return prev;
            }

            const currentStream = prev[sessionId] || { text: '', isComplete: false };
            const incomingChunk = data.chunk || '';

            if (data.is_final) {
              console.log(`Chat stream completed for session ${sessionId}. Final text length: ${(currentStream.text + incomingChunk).length}`);
              return {
                ...prev,
                [sessionId]: {
                  text: currentStream.text + incomingChunk,
                  isComplete: true,
                  metadata: data.metadata,
                  timestamp: new Date().toISOString()
                }
              };
            } else {
              // Detect if chunks are cumulative or incremental
              // If the incoming chunk starts with the current text, it's cumulative
              // Otherwise, it's incremental and should be concatenated
              let newText: string;

              if (incomingChunk.startsWith(currentStream.text) && currentStream.text.length > 0) {
                // Cumulative chunk - use the incoming chunk as-is
                newText = incomingChunk;
                console.log(`Cumulative chunk for session ${sessionId}. Length: ${newText.length}`);
              } else {
                // Incremental chunk - concatenate with existing text
                newText = currentStream.text + incomingChunk;
                console.log(`Incremental chunk for session ${sessionId}. Current length: ${newText.length}, Added: "${incomingChunk}"`);
              }

              return {
                ...prev,
                [sessionId]: {
                  ...currentStream,
                  text: newText,
                  isComplete: false,
                  timestamp: new Date().toISOString()
                }
              };
            }
          });
        } catch (error) {
          console.error('Error processing chat response chunk:', error, data);
        }
      });

      // Chat error events
      socket.on(WEBSOCKET_EVENTS.CHAT_ERROR, (data: any) => {
        console.error('Chat error received:', data);
        const sessionId = data.chat_session_id;
        if (sessionId) {
          setChatStreams(prev => ({
            ...prev,
            [sessionId]: {
              text: `Error: ${data.message || 'Unknown chat error'}`,
              isComplete: true,
              metadata: { error: true, ...data.metadata },
              timestamp: new Date().toISOString()
            }
          }));
        }
      });

      socket.on(WEBSOCKET_EVENTS.ERROR, (data: any) => {
        logWebSocketEvent('Error', data);
        const errorMessage = formatConnectionError(data);
        setConnectionError(errorMessage);
      });

      // System message events
      socket.on(WEBSOCKET_EVENTS.SYSTEM_MESSAGE, (data: any) => {
        logWebSocketEvent('System message', data);
        const systemMessage = {
          id: `sys-${Date.now()}`,
          type: data.type || 'info',
          message: data.message || 'System notification',
          timestamp: new Date().toISOString(),
          metadata: data.metadata
        };
        setSystemMessages(prev => [...prev.slice(-19), systemMessage]); // Keep last 20 messages
      });

      // Rate limit events
      socket.on(WEBSOCKET_EVENTS.RATE_LIMIT, (data: any) => {
        logWebSocketEvent('Rate limit', data);
        const rateLimitMessage = {
          id: `rate-${Date.now()}`,
          type: 'warning',
          message: `Rate limit exceeded: ${data.message || 'Please slow down'}`,
          timestamp: new Date().toISOString(),
          metadata: data
        };
        setSystemMessages(prev => [...prev.slice(-19), rateLimitMessage]);
      });

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionError((error as Error).message);
    }
  }, [userId]);

  // Room management
  const joinFileRoom = useCallback((fileId: string) => {
    if (socketRef.current?.connected && !joinedFileRooms.current.has(fileId)) {
      logWebSocketEvent('Joining file room', { fileId });
      socketRef.current.emit(WEBSOCKET_EVENTS.JOIN_FILE_ROOM, { file_id: fileId });
      joinedFileRooms.current.add(fileId);
    }
  }, []);

  const joinChatRoom = useCallback((chatSessionId: string) => {
    if (socketRef.current?.connected && !joinedChatRooms.current.has(chatSessionId)) {
      logWebSocketEvent('Joining chat room', { chatSessionId });
      socketRef.current.emit(WEBSOCKET_EVENTS.JOIN_CHAT_ROOM, { chat_session_id: chatSessionId });
      joinedChatRooms.current.add(chatSessionId);
    }
  }, []);

  // Leave rooms
  const leaveFileRoom = useCallback((fileId: string) => {
    if (socketRef.current?.connected && joinedFileRooms.current.has(fileId)) {
      logWebSocketEvent('Leaving file room', { fileId });
      socketRef.current.emit(WEBSOCKET_EVENTS.LEAVE_FILE_ROOM, { file_id: fileId });
      joinedFileRooms.current.delete(fileId);
    }
  }, []);

  const leaveChatRoom = useCallback((chatSessionId: string) => {
    if (socketRef.current?.connected && joinedChatRooms.current.has(chatSessionId)) {
      logWebSocketEvent('Leaving chat room', { chatSessionId });
      socketRef.current.emit(WEBSOCKET_EVENTS.LEAVE_CHAT_ROOM, { chat_session_id: chatSessionId });
      joinedChatRooms.current.delete(chatSessionId);
    }
  }, []);

  // Clear chat streams for a specific session
  const clearChatStream = useCallback((sessionId: string) => {
    setChatStreams(prev => {
      const updated = { ...prev };
      delete updated[sessionId];
      return updated;
    });
    // Also leave the chat room to prevent further messages
    leaveChatRoom(sessionId);
  }, [leaveChatRoom]);

  // Clear all chat streams
  const clearAllChatStreams = useCallback(() => {
    logWebSocketEvent('Clearing all chat streams');
    setChatStreams({});
    // Leave all chat rooms
    joinedChatRooms.current.forEach(sessionId => {
      if (socketRef.current?.connected) {
        socketRef.current.emit(WEBSOCKET_EVENTS.LEAVE_CHAT_ROOM, { chat_session_id: sessionId });
      }
    });
    joinedChatRooms.current.clear();
  }, []);

  // System message utilities
  const getSystemMessages = useCallback(() => {
    return systemMessages;
  }, [systemMessages]);

  const clearSystemMessages = useCallback(() => {
    setSystemMessages([]);
  }, []);

  const getUnreadSystemMessages = useCallback(() => {
    return systemMessages.filter(msg => msg.type === 'error' || msg.type === 'warning');
  }, [systemMessages]);

  // Data access methods
  const getFileStatus = useCallback((fileId: string) => fileUpdates[fileId] || null, [fileUpdates]);
  const getProcessingProgress = useCallback((fileId: string) => processingProgress[fileId] || null, [processingProgress]);
  const getChatStream = useCallback((sessionId: string) => {
    const stream = chatStreams[sessionId];
    if (!stream) return null;
    return {
      text: stream.text,
      isComplete: stream.isComplete,
      metadata: stream.metadata,
      timestamp: stream.timestamp
    };
  }, [chatStreams]);

  // Initialize connection
  useEffect(() => {
    if (userId) {
      connect();
    }
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [userId, connect]);

  // Manual connection control
  const reconnect = useCallback(() => {
    logWebSocketEvent('Manual reconnection requested');
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
    reconnectAttempts.current = 0;
    connect();
  }, [connect]);

  const disconnect = useCallback(() => {
    logWebSocketEvent('Manual disconnection requested');
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
    setIsConnected(false);
    setConnectionError(null);
  }, []);

  return useMemo(() => ({
    isConnected,
    connectionError,
    joinFileRoom,
    joinChatRoom,
    leaveFileRoom,
    leaveChatRoom,
    getFileStatus,
    getProcessingProgress,
    getChatStream,
    clearChatStream,
    clearAllChatStreams,
    fileUpdates,
    processingProgress,
    chatStreams,
    reconnect,
    disconnect,
    // System message functions
    systemMessages,
    getSystemMessages,
    clearSystemMessages,
    getUnreadSystemMessages
  }), [
    isConnected,
    connectionError,
    joinFileRoom,
    joinChatRoom,
    leaveFileRoom,
    leaveChatRoom,
    getFileStatus,
    getProcessingProgress,
    getChatStream,
    clearChatStream,
    clearAllChatStreams,
    fileUpdates,
    processingProgress,
    chatStreams,
    reconnect,
    disconnect,
    systemMessages,
    getSystemMessages,
    clearSystemMessages,
    getUnreadSystemMessages
  ]);
};
